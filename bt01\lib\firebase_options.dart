// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDxd-xjgi9l9AS6rgL9TE27ngtzaUAlMsM',
    appId: '1:407795426004:web:3d38547364411373561141',
    messagingSenderId: '407795426004',
    projectId: 'my-firebase-app-27fd5',
    authDomain: 'my-firebase-app-27fd5.firebaseapp.com',
    storageBucket: 'my-firebase-app-27fd5.firebasestorage.app',
    measurementId: 'G-9RP7J9MB2G',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAUF8Kivbx2sXdTiMPLly8iw4LXvK3mT-A',
    appId: '1:407795426004:android:87d52ad655eff23d561141',
    messagingSenderId: '407795426004',
    projectId: 'my-firebase-app-27fd5',
    storageBucket: 'my-firebase-app-27fd5.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDMZvx9j8uwCMJjOsJBv_DeYLwCQLqfDgg',
    appId: '1:407795426004:ios:1d780be9e2b8d5da561141',
    messagingSenderId: '407795426004',
    projectId: 'my-firebase-app-27fd5',
    storageBucket: 'my-firebase-app-27fd5.firebasestorage.app',
    iosBundleId: 'com.example.bt01',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDMZvx9j8uwCMJjOsJBv_DeYLwCQLqfDgg',
    appId: '1:407795426004:ios:1d780be9e2b8d5da561141',
    messagingSenderId: '407795426004',
    projectId: 'my-firebase-app-27fd5',
    storageBucket: 'my-firebase-app-27fd5.firebasestorage.app',
    iosBundleId: 'com.example.bt01',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDxd-xjgi9l9AS6rgL9TE27ngtzaUAlMsM',
    appId: '1:407795426004:web:a6e53cdf8827d870561141',
    messagingSenderId: '407795426004',
    projectId: 'my-firebase-app-27fd5',
    authDomain: 'my-firebase-app-27fd5.firebaseapp.com',
    storageBucket: 'my-firebase-app-27fd5.firebasestorage.app',
    measurementId: 'G-XYNLGZVLXL',
  );
}
