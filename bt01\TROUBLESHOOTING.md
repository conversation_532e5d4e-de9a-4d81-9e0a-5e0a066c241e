# Troubleshooting - Appwrite App

## 🔧 Các vấn đề đã được sửa

### 1. **Vấn đề: Tà<PERSON> khoản được tạo nhưng hiện thông báo lỗi**

**Nguyên nhân:**
- <PERSON>ng đột giữa `AuthWrapper` và việc điều hướng thủ công trong screens
- Race condition: AuthWrapper chuyển screen trước khi LoginScreen xử lý xong response
- File `google-services.json` ở sai vị trí (`android/app/src/` thay vì `android/app/`)

**Giải pháp đã áp dụng:**
- Loại bỏ việc điều hướng thủ công (`Navigator.pushReplacement`) trong `LoginScreen` và `RegisterScreen`
- Để `AuthWrapper` trong `main.dart` tự động xử lý điều hướng dựa trên `authStateChanges`
- <PERSON>h<PERSON><PERSON> thông báo thành công khi đăng ký/đăng nhập thành công

**Code đã sửa:**
```dart
// Trước (có vấn đề)
await _authService.registerWithEmailAndPassword(...);
Navigator.of(context).pushReplacement(...); // Xung đột với AuthWrapper

// Sau (đã sửa)
final result = await _authService.registerWithEmailAndPassword(...);
if (mounted && result != null) {
  ScaffoldMessenger.of(context).showSnackBar(...); // Chỉ hiển thị thông báo
  // AuthWrapper sẽ tự động điều hướng
}
```

### 2. **Vấn đề: Thông báo lỗi không rõ ràng**

**Nguyên nhân:** Xử lý lỗi Firebase Auth chưa chi tiết.

**Giải pháp đã áp dụng:**
- Cải thiện method `_handleAuthException` với thông báo lỗi chi tiết hơn
- Thêm nhiều error codes Firebase Auth
- Sử dụng `debugPrint` để debug

**Các lỗi được xử lý:**
- `email-already-in-use`: "Email này đã được đăng ký. Vui lòng sử dụng email khác hoặc đăng nhập."
- `user-not-found`: "Không tìm thấy tài khoản với email này. Vui lòng kiểm tra lại email hoặc đăng ký tài khoản mới."
- `wrong-password`: "Mật khẩu không đúng. Vui lòng thử lại."
- `invalid-credential`: "Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra lại email và mật khẩu."
- `network-request-failed`: "Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại."

### 3. **Vấn đề: Build errors với NDK và Kotlin versions**

**Nguyên nhân:** Incompatible versions giữa Flutter, Firebase, và Android build tools.

**Giải pháp đã áp dụng:**
- Downgrade Firebase versions để tương thích:
  - `firebase_core: ^2.24.2`
  - `firebase_auth: ^4.15.3`
  - `google_sign_in: ^6.1.6`
- Sử dụng `ndkVersion = flutter.ndkVersion` thay vì hard-code
- Cập nhật `minSdk = 23` cho Firebase Auth
- Sử dụng Firebase BOM `32.7.0`

## 🚀 Cách test ứng dụng

### Test Đăng ký:
1. Mở ứng dụng
2. Nhấn "Create Account"
3. Nhập email và password hợp lệ
4. Nhấn "Đăng Ký"
5. **Kết quả mong đợi:** Thông báo "Đăng ký thành công!" và tự động chuyển đến HomeScreen

### Test Đăng nhập:
1. Từ màn hình đăng nhập
2. Nhập email và password đã đăng ký
3. Nhấn "Sign In"
4. **Kết quả mong đợi:** Thông báo "Đăng nhập thành công!" và tự động chuyển đến HomeScreen

### Test lỗi:
1. **Email đã tồn tại:** Đăng ký với email đã có → Hiển thị thông báo rõ ràng
2. **Sai mật khẩu:** Đăng nhập với password sai → Hiển thị "Mật khẩu không đúng"
3. **Email không tồn tại:** Đăng nhập với email chưa đăng ký → Hiển thị "Không tìm thấy tài khoản"

## 🔍 Debug tips

### Kiểm tra authentication state:
```dart
// Trong AuthService
void debugAuthState() {
  final user = currentUser;
  if (user != null) {
    debugPrint('User logged in: ${user.email}, UID: ${user.uid}');
  } else {
    debugPrint('No user logged in');
  }
}
```

### Xem Firebase Auth logs:
- Mở Flutter DevTools
- Vào tab Console
- Tìm logs có prefix "Firebase Auth Error:"

### Kiểm tra Firebase Console:
1. Vào [Firebase Console](https://console.firebase.google.com/)
2. Chọn project
3. Vào Authentication > Users
4. Kiểm tra xem user có được tạo không

## ⚠️ Lưu ý quan trọng

### 1. AuthWrapper hoạt động như thế nào:
```dart
StreamBuilder<User?>(
  stream: FirebaseAuth.instance.authStateChanges(),
  builder: (context, snapshot) {
    if (snapshot.hasData) {
      return const HomeScreen(); // User đã đăng nhập
    }
    return const LoginScreen(); // User chưa đăng nhập
  },
);
```

### 2. Không điều hướng thủ công:
- ❌ **Không làm:** `Navigator.pushReplacement` sau khi auth thành công
- ✅ **Nên làm:** Để AuthWrapper tự động điều hướng

### 3. Xử lý loading state:
- Hiển thị loading indicator khi đang xử lý auth
- Disable button để tránh multiple requests
- Hiển thị thông báo thành công/lỗi rõ ràng

## 🔄 Workflow hoàn chỉnh

1. **User nhấn đăng ký/đăng nhập**
2. **Loading state** được hiển thị
3. **Firebase Auth** xử lý request
4. **Nếu thành công:**
   - Hiển thị thông báo thành công
   - `authStateChanges` stream emit user mới
   - `AuthWrapper` tự động điều hướng đến HomeScreen
5. **Nếu lỗi:**
   - Hiển thị thông báo lỗi chi tiết
   - User có thể thử lại

Với các sửa đổi này, ứng dụng sẽ hoạt động mượt mà và cung cấp trải nghiệm người dùng tốt!
