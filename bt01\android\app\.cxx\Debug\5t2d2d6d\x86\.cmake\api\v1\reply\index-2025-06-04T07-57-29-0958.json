{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/File/FileAndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-3430dfaa108a3b9e7f53.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-f46df334662de374ee7c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-60c6c5a6cfa6fcc209d9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-f46df334662de374ee7c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-60c6c5a6cfa6fcc209d9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-3430dfaa108a3b9e7f53.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}