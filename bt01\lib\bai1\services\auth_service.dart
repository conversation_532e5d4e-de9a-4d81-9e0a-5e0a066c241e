import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Lấy user hiện tại
  User? get currentUser => _auth.currentUser;

  // Stream để lắng nghe thay đổi trạng thái đăng nhập
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Đăng ký với email và password
  Future<UserCredential?> registerWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      debugPrint('🔥 AuthService - Bắt đầu đăng ký Firebase...');
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      debugPrint('🔥 AuthService - Đ<PERSON><PERSON> ký thành công: ${result.user?.email}');
      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        '🔥 AuthService - FirebaseAuthException: ${e.code} - ${e.message}',
      );
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('🔥 AuthService - Unknown error: $e');
      debugPrint('🔥 AuthService - Error type: ${e.runtimeType}');
      throw 'Đã xảy ra lỗi không xác định: $e';
    }
  }

  // Đăng nhập với email và password
  Future<UserCredential?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      debugPrint('🔥 AuthService - Bắt đầu đăng nhập Firebase...');
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      debugPrint(
        '🔥 AuthService - Đăng nhập thành công: ${result.user?.email}',
      );
      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        '🔥 AuthService - FirebaseAuthException: ${e.code} - ${e.message}',
      );
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('🔥 AuthService - Unknown error: $e');
      debugPrint('🔥 AuthService - Error type: ${e.runtimeType}');
      throw 'Đã xảy ra lỗi không xác định: $e';
    }
  }

  // Đăng nhập với Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return null; // User cancelled the sign-in
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Once signed in, return the UserCredential
      return await _auth.signInWithCredential(credential);
    } catch (e) {
      throw 'Đăng nhập Google thất bại: $e';
    }
  }

  // Đăng xuất
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      throw 'Đăng xuất thất bại';
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Đã xảy ra lỗi không xác định';
    }
  }

  // Debug method để kiểm tra trạng thái authentication
  void debugAuthState() {
    final user = currentUser;
    if (user != null) {
      debugPrint('User logged in: ${user.email}, UID: ${user.uid}');
    } else {
      debugPrint('No user logged in');
    }
  }

  // Xử lý lỗi Firebase Auth
  String _handleAuthException(FirebaseAuthException e) {
    debugPrint('Firebase Auth Error: ${e.code} - ${e.message}'); // Debug log

    switch (e.code) {
      case 'weak-password':
        return 'Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.';
      case 'email-already-in-use':
        return 'Email này đã được đăng ký. Vui lòng sử dụng email khác hoặc đăng nhập.';
      case 'user-not-found':
        return 'Không tìm thấy tài khoản với email này. Vui lòng kiểm tra lại email hoặc đăng ký tài khoản mới.';
      case 'wrong-password':
        return 'Mật khẩu không đúng. Vui lòng thử lại.';
      case 'invalid-email':
        return 'Định dạng email không hợp lệ. Vui lòng nhập email đúng định dạng.';
      case 'user-disabled':
        return 'Tài khoản này đã bị vô hiệu hóa. Vui lòng liên hệ hỗ trợ.';
      case 'too-many-requests':
        return 'Quá nhiều yêu cầu đăng nhập. Vui lòng thử lại sau ít phút.';
      case 'operation-not-allowed':
        return 'Phương thức đăng nhập này không được phép. Vui lòng liên hệ hỗ trợ.';
      case 'invalid-credential':
        return 'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra lại email và mật khẩu.';
      case 'account-exists-with-different-credential':
        return 'Tài khoản đã tồn tại với phương thức đăng nhập khác. Vui lòng sử dụng phương thức đăng nhập ban đầu.';
      case 'requires-recent-login':
        return 'Vui lòng đăng nhập lại để thực hiện thao tác này.';
      case 'network-request-failed':
        return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      case 'internal-error':
        return 'Lỗi hệ thống. Vui lòng thử lại sau.';
      default:
        return 'Đã xảy ra lỗi: ${e.message ?? 'Lỗi không xác định'}';
    }
  }
}
