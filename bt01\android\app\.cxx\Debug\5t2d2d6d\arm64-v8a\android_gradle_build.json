{"buildFiles": ["D:\\File\\FileFlutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Firebase\\bt01\\android\\app\\.cxx\\Debug\\5t2d2d6d\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Firebase\\bt01\\android\\app\\.cxx\\Debug\\5t2d2d6d\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\File\\FileAndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\File\\FileAndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}