{"buildFiles": ["D:\\File\\FileFlutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Firebase\\bt01\\android\\app\\.cxx\\Debug\\5t2d2d6d\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Firebase\\bt01\\android\\app\\.cxx\\Debug\\5t2d2d6d\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}