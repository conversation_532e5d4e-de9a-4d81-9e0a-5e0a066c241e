# Hướng dẫn cấu hình Firebase cho Appwrite App

## Bước 1: Tạo Firebase Project

1. <PERSON><PERSON><PERSON> cậ<PERSON> [Firebase Console](https://console.firebase.google.com/)
2. <PERSON>hấn "Add project" hoặc "Tạo dự án"
3. <PERSON><PERSON><PERSON><PERSON> tên dự án (ví dụ: "Appwrite App")
4. <PERSON><PERSON><PERSON> có sử dụng Google Analytics (tùy chọn)
5. Nhấn "Create project"

## Bước 2: Thêm ứng dụng Android

1. Trong Firebase Console, nhấn icon Android
2. Nhập thông tin:
   - **Android package name**: `com.example.bt01`
   - **App nickname**: `Appwrite App` (tùy chọn)
   - **Debug signing certificate SHA-1**: (tùy chọn, có thể bỏ qua)
3. Nhấn "Register app"
4. Tải file `google-services.json`
5. Đặt file này vào thư mục `android/app/`

## Bước 3: <PERSON><PERSON><PERSON> hình Authentication

1. Trong Firebase Console, vào **Authentication**
2. Nhấn tab **Sign-in method**
3. <PERSON><PERSON><PERSON> các phương thức đăng nhập:

### Email/Password
- Nhấn "Email/Password"
- Bật "Enable"
- Nhấn "Save"

### Google Sign-In
- Nhấn "Google"
- Bật "Enable"
- Chọn email hỗ trợ dự án
- Nhấn "Save"

## Bước 4: Cấu hình Google Sign-In (Chi tiết)

### Lấy Web Client ID
1. Vào [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project Firebase của bạn
3. Vào **APIs & Services** > **Credentials**
4. Tìm "Web client" trong OAuth 2.0 Client IDs
5. Copy Client ID

### Cập nhật android/app/build.gradle.kts
Đảm bảo file có nội dung sau:

```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:33.14.0"))
    implementation("com.google.firebase:firebase-analytics")
}

android {
    namespace = "com.example.bt01"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    defaultConfig {
        applicationId = "com.example.bt01"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }
}
```

## Bước 5: Chạy FlutterFire CLI (Tùy chọn)

Nếu bạn có FlutterFire CLI:

```bash
# Cài đặt FlutterFire CLI
dart pub global activate flutterfire_cli

# Cấu hình Firebase
flutterfire configure
```

## Bước 6: Kiểm tra cấu hình

1. Đảm bảo file `google-services.json` đã được đặt trong `android/app/`
2. Đảm bảo file `firebase_options.dart` tồn tại trong `lib/`
3. Chạy ứng dụng:

```bash
flutter clean
flutter pub get
flutter run
```

## Troubleshooting

### Lỗi "google-services.json not found"
- Đảm bảo file `google-services.json` được đặt đúng trong `android/app/`
- Restart IDE và chạy `flutter clean`

### Lỗi "minSdkVersion"
- Đã được sửa trong `android/app/build.gradle.kts` với `minSdk = 23`

### Lỗi Google Sign-In
- Đảm bảo đã bật Google Sign-In trong Firebase Console
- Kiểm tra SHA-1 fingerprint nếu cần thiết
- Đảm bảo package name khớp với Firebase config

### Lỗi NDK version
- Đã được sửa với `ndkVersion = "27.0.12077973"`

## Cấu trúc file sau khi hoàn thành

```
android/
├── app/
│   ├── google-services.json  ← File từ Firebase
│   └── build.gradle.kts      ← Đã cập nhật
└── build.gradle.kts

lib/
├── firebase_options.dart     ← Tự động tạo hoặc từ FlutterFire
└── main.dart                 ← Đã cập nhật
```

## Test các chức năng

1. **Đăng ký**: Tạo tài khoản mới với email/password
2. **Đăng nhập**: Đăng nhập với tài khoản đã tạo
3. **Google Sign-In**: Đăng nhập bằng tài khoản Google
4. **Xem như khách**: Truy cập ứng dụng mà không đăng nhập
5. **Đăng xuất**: Thoát khỏi tài khoản

Sau khi hoàn thành các bước trên, ứng dụng sẽ hoạt động đầy đủ với Firebase Authentication!
